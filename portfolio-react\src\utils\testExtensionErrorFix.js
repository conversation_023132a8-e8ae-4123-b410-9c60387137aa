/**
 * Test Script to Verify Extension Error Fix
 * 
 * This script can be run in the browser console to verify that
 * the extension error handling is working correctly.
 */

import { 
  isExtensionError, 
  safeFetch, 
  extensionSafePromise,
  initializeExtensionErrorHandling 
} from './extensionErrorHandler';

/**
 * Simulate the exact error that was occurring
 */
export const simulateOriginalError = () => {
  console.log('🧪 Testing Extension Error Detection');
  
  const originalError = new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
  
  console.log('Original error message:', originalError.message);
  console.log('Is detected as extension error:', isExtensionError(originalError));
  
  if (isExtensionError(originalError)) {
    console.log('✅ SUCCESS: Original error is correctly identified as extension error');
  } else {
    console.log('❌ FAIL: Original error not detected as extension error');
  }
  
  return isExtensionError(originalError);
};

/**
 * Test that the error would be caught by global handlers
 */
export const testGlobalErrorHandling = () => {
  console.log('\n🧪 Testing Global Error Handling');
  
  // Initialize error handling
  initializeExtensionErrorHandling();
  
  // Test that extension errors are suppressed
  console.log('Throwing extension error (should be suppressed)...');
  
  setTimeout(() => {
    try {
      throw new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
    } catch (error) {
      // This should be caught by our global handler
      console.log('Extension error thrown - check if it appears in console');
    }
  }, 100);
  
  // Test that regular errors still appear
  setTimeout(() => {
    console.log('Throwing regular error (should appear in console)...');
    setTimeout(() => {
      throw new Error('This is a regular application error - should appear');
    }, 50);
  }, 200);
};

/**
 * Test safeFetch with simulated extension error
 */
export const testSafeFetch = async () => {
  console.log('\n🧪 Testing Safe Fetch');
  
  // Mock fetch to simulate extension error
  const originalFetch = window.fetch;
  
  // Test 1: Simulate extension error
  window.fetch = jest.fn().mockRejectedValue(
    new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received')
  );
  
  try {
    const result = await safeFetch('/test-url');
    console.log('SafeFetch result with extension error:', result.status);
    
    if (result.status === 0) {
      console.log('✅ SUCCESS: Extension error handled by safeFetch');
    } else {
      console.log('❌ FAIL: Extension error not handled properly');
    }
  } catch (error) {
    console.log('❌ FAIL: Extension error not caught by safeFetch:', error.message);
  }
  
  // Test 2: Simulate regular error
  window.fetch = jest.fn().mockRejectedValue(new Error('Network timeout'));
  
  try {
    await safeFetch('/test-url');
    console.log('❌ FAIL: Regular error should have been thrown');
  } catch (error) {
    if (error.message === 'Network timeout') {
      console.log('✅ SUCCESS: Regular error properly thrown by safeFetch');
    } else {
      console.log('❌ FAIL: Unexpected error from safeFetch:', error.message);
    }
  }
  
  // Restore original fetch
  window.fetch = originalFetch;
};

/**
 * Test admin route protection
 */
export const testAdminRouteProtection = async () => {
  console.log('\n🧪 Testing Admin Route Protection');
  
  // This would normally be called by admin components
  const mockAdminFetch = async () => {
    try {
      // This simulates what happens in AllVisitorsDetails and other admin components
      const response = await safeFetch('/api/admin/dashboard', {
        headers: { 'Authorization': 'Bearer test-token' }
      });
      
      console.log('Admin fetch completed without extension errors');
      return response;
    } catch (error) {
      if (isExtensionError(error)) {
        console.log('✅ SUCCESS: Extension error suppressed in admin route');
        return null;
      } else {
        console.log('Regular error in admin route:', error.message);
        throw error;
      }
    }
  };
  
  await mockAdminFetch();
};

/**
 * Run all tests
 */
export const runComprehensiveTest = async () => {
  console.log('🚀 Running Comprehensive Extension Error Fix Test\n');
  console.log('This test verifies that the fix for the error:');
  console.log('"A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received"');
  console.log('is working correctly.\n');
  
  try {
    // Test 1: Error detection
    const detectionResult = simulateOriginalError();
    
    // Test 2: Global error handling
    testGlobalErrorHandling();
    
    // Test 3: Safe fetch
    if (typeof jest !== 'undefined') {
      await testSafeFetch();
    } else {
      console.log('⚠️ Skipping safeFetch test (Jest not available)');
    }
    
    // Test 4: Admin route protection
    await testAdminRouteProtection();
    
    console.log('\n📊 Test Summary:');
    console.log('✅ Error detection:', detectionResult ? 'PASS' : 'FAIL');
    console.log('✅ Global error handling: Initialized');
    console.log('✅ Admin route protection: Applied');
    console.log('\n🎉 Extension error fix verification complete!');
    console.log('The error should no longer appear in the console.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

// Make functions available in browser console
if (typeof window !== 'undefined') {
  window.testExtensionErrorFix = {
    simulateOriginalError,
    testGlobalErrorHandling,
    testSafeFetch,
    testAdminRouteProtection,
    runComprehensiveTest
  };
  
  console.log('🧪 Extension Error Fix Test utilities loaded!');
  console.log('Run window.testExtensionErrorFix.runComprehensiveTest() to test the fix');
}

export default {
  simulateOriginalError,
  testGlobalErrorHandling,
  testSafeFetch,
  testAdminRouteProtection,
  runComprehensiveTest
};
