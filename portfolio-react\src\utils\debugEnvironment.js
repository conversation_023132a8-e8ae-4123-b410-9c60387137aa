/**
 * Debug Environment Utility
 * 
 * This utility helps debug environment variables and API configuration
 * in both development and production environments.
 */

import { logUserFriendly, logSensitive } from './logger';
import { API_CONFIG } from '../config/apiConfig';

/**
 * Logs all environment configuration for debugging
 */
export const debugEnvironment = () => {
  // Only show debug info in development
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 Environment Debug Information:');
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('REACT_APP_API_URL (env):', process.env.REACT_APP_API_URL);
    console.log('API_CONFIG.BASE_URL (actual):', API_CONFIG.BASE_URL);
    console.log('Current URL:', window.location.href);
    console.log('Current Origin:', window.location.origin);
  }

  // Check if API URL is properly configured (now using config)
  if (!API_CONFIG.BASE_URL) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ API_CONFIG.BASE_URL is not set!');
    }
    return false;
  }

  if (process.env.NODE_ENV === 'development') {
    console.log('✅ API URL is configured via config');
  }
  return true;
};

/**
 * Tests the API connection from the frontend
 */
export const testAPIConnection = async () => {
  const API_URL = API_CONFIG.BASE_URL;

  if (!API_URL) {
    if (process.env.NODE_ENV === 'development') {
      logUserFriendly('❌ No API URL configured');
    }
    return false;
  }

  if (process.env.NODE_ENV === 'development') {
    logUserFriendly(`🔍 Testing API connection to: ${API_URL}`);
  }

  try {
    const response = await fetch(API_CONFIG.ENDPOINTS.PING, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      if (process.env.NODE_ENV === 'development') {
        logUserFriendly('✅ API connection successful!');
        logSensitive('API Response:', data);
      }
      return true;
    } else {
      if (process.env.NODE_ENV === 'development') {
        logUserFriendly(`❌ API connection failed with status: ${response.status}`);
      }
      return false;
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      logUserFriendly('❌ API connection error:', error.message);
    }
    return false;
  }
};

/**
 * Comprehensive environment and API test
 */
export const runEnvironmentDiagnostics = async () => {
  // Only run full diagnostics in development
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 Running Environment Diagnostics...\n');

    // Test 1: Environment variables
    console.log('1️⃣ Environment Variables:');
    const envOk = debugEnvironment();

    // Test 2: API connection
    console.log('\n2️⃣ API Connection:');
    const apiOk = await testAPIConnection();

    // Test 3: Browser environment
    console.log('\n3️⃣ Browser Environment:');
    console.log('User Agent:', navigator.userAgent);
    console.log('Online:', navigator.onLine);
    console.log('Protocol:', window.location.protocol);

    // Summary
    console.log('\n📊 Diagnostics Summary:');
    console.log('Environment Config:', envOk ? '✅' : '❌');
    console.log('API Connection:', apiOk ? '✅' : '❌');

    return { envOk, apiOk };
  } else {
    // In production, just silently check if everything is working
    const envOk = debugEnvironment();
    const apiOk = await testAPIConnection();
    return { envOk, apiOk };
  }
};

export default {
  debugEnvironment,
  testAPIConnection,
  runEnvironmentDiagnostics
};
