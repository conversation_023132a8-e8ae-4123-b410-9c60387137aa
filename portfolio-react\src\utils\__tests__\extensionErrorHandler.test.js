/**
 * Tests for Extension Error Handler
 */

import {
  isExtensionError,
  safeExecute,
  extensionSafePromise
} from '../extensionErrorHandler';

describe('Extension Error Handler', () => {
  describe('isExtensionError', () => {
    test('should identify extension errors correctly', () => {
      const extensionErrors = [
        'Could not establish connection',
        'A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received',
        'Extension context invalidated',
        'The message port closed before a response was received',
        'chrome-extension://abc123/script.js',
        'moz-extension://def456/content.js',
        'Receiving end does not exist'
      ];

      extensionErrors.forEach(error => {
        expect(isExtensionError(error)).toBe(true);
      });
    });

    test('should not identify regular errors as extension errors', () => {
      const regularErrors = [
        'Network error',
        'TypeError: Cannot read property',
        'ReferenceError: variable is not defined',
        'SyntaxError: Unexpected token',
        'Failed to fetch'
      ];

      regularErrors.forEach(error => {
        expect(isExtensionError(error)).toBe(false);
      });
    });

    test('should handle Error objects', () => {
      const extensionError = new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
      const regularError = new Error('Network timeout');

      expect(isExtensionError(extensionError)).toBe(true);
      expect(isExtensionError(regularError)).toBe(false);
    });
  });

  describe('safeExecute', () => {
    test('should return result for successful execution', async () => {
      const callback = jest.fn().mockResolvedValue('success');
      const result = await safeExecute(callback, 'test');
      
      expect(result).toBe('success');
      expect(callback).toHaveBeenCalled();
    });

    test('should return null for extension errors', async () => {
      const callback = jest.fn().mockRejectedValue(
        new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received')
      );
      
      const result = await safeExecute(callback, 'test');
      
      expect(result).toBe(null);
      expect(callback).toHaveBeenCalled();
    });

    test('should re-throw non-extension errors', async () => {
      const callback = jest.fn().mockRejectedValue(new Error('Network error'));
      
      await expect(safeExecute(callback, 'test')).rejects.toThrow('Network error');
      expect(callback).toHaveBeenCalled();
    });
  });

  describe('extensionSafePromise', () => {
    test('should resolve normally for successful promises', async () => {
      const promise = Promise.resolve('success');
      const result = await extensionSafePromise(promise);
      
      expect(result).toBe('success');
    });

    test('should return fallback for extension errors', async () => {
      const promise = Promise.reject(
        new Error('Extension context invalidated')
      );
      
      const result = await extensionSafePromise(promise, 'fallback');
      
      expect(result).toBe('fallback');
    });

    test('should re-throw non-extension errors', async () => {
      const promise = Promise.reject(new Error('Network error'));
      
      await expect(extensionSafePromise(promise)).rejects.toThrow('Network error');
    });
  });
});
