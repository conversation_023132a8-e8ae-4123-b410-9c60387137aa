/**
 * Advanced Extension Error Interceptor
 * 
 * This utility provides the most comprehensive protection against browser extension errors
 * by intercepting them at multiple levels before they reach the console.
 */

import { logDebug } from './logger';
import { isExtensionError } from './extensionErrorHandler';

/**
 * Intercept and suppress extension errors at the browser level
 */
export const initializeAdvancedExtensionInterception = () => {
  if (window.__advancedExtensionInterceptionActive) return;

  // 1. Override the native Promise rejection handler
  const originalPromiseRejectionHandler = window.onunhandledrejection;
  window.onunhandledrejection = function(event) {
    const reason = event.reason;
    let message = '';
    
    if (reason) {
      if (typeof reason === 'string') {
        message = reason;
      } else if (reason.message) {
        message = reason.message;
      } else if (reason.toString) {
        message = reason.toString();
      }
    }
    
    if (isExtensionError(message)) {
      logDebug('Advanced interceptor: Extension promise rejection suppressed', message);
      event.preventDefault();
      return true;
    }
    
    // Call original handler if it exists
    if (originalPromiseRejectionHandler) {
      return originalPromiseRejectionHandler.call(this, event);
    }
  };

  // 2. Override the native error handler
  const originalErrorHandler = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    const errorMessage = message || (error && error.message) || '';
    const sourceFile = source || '';
    
    if (isExtensionError(errorMessage) || 
        sourceFile.includes('extension://') ||
        sourceFile.includes('chrome-extension') ||
        sourceFile.includes('moz-extension')) {
      logDebug('Advanced interceptor: Extension error suppressed', errorMessage);
      return true; // Prevent default error handling
    }
    
    // Call original handler if it exists
    if (originalErrorHandler) {
      return originalErrorHandler.call(this, message, source, lineno, colno, error);
    }
    
    return false;
  };

  // 3. Intercept fetch requests that might be interfered with by extensions
  const originalFetch = window.fetch;
  window.fetch = async function(...args) {
    try {
      return await originalFetch.apply(this, args);
    } catch (error) {
      if (isExtensionError(error)) {
        logDebug('Advanced interceptor: Extension fetch error suppressed', error.message);
        // Return a mock response to prevent the error from propagating
        return new Response(JSON.stringify({ error: 'Extension interference detected' }), {
          status: 200,
          statusText: 'OK',
          headers: { 'Content-Type': 'application/json' }
        });
      }
      throw error;
    }
  };

  // 4. Intercept XMLHttpRequest for older extension interference
  const originalXHROpen = XMLHttpRequest.prototype.open;
  const originalXHRSend = XMLHttpRequest.prototype.send;
  
  XMLHttpRequest.prototype.open = function(...args) {
    this._extensionSafe = true;
    return originalXHROpen.apply(this, args);
  };
  
  XMLHttpRequest.prototype.send = function(...args) {
    if (this._extensionSafe) {
      const originalOnError = this.onerror;
      this.onerror = function(event) {
        const error = event.error || event.message || 'XHR Error';
        if (isExtensionError(error)) {
          logDebug('Advanced interceptor: Extension XHR error suppressed', error);
          return;
        }
        if (originalOnError) {
          originalOnError.call(this, event);
        }
      };
    }
    return originalXHRSend.apply(this, args);
  };

  // 5. Intercept WebSocket errors that might be caused by extensions
  const originalWebSocket = window.WebSocket;
  if (originalWebSocket) {
    window.WebSocket = function(...args) {
      const ws = new originalWebSocket(...args);
      const originalOnError = ws.onerror;
      
      ws.onerror = function(event) {
        const error = event.error || event.message || 'WebSocket Error';
        if (isExtensionError(error)) {
          logDebug('Advanced interceptor: Extension WebSocket error suppressed', error);
          return;
        }
        if (originalOnError) {
          originalOnError.call(this, event);
        }
      };
      
      return ws;
    };
    
    // Copy static properties
    Object.setPrototypeOf(window.WebSocket, originalWebSocket);
    Object.defineProperty(window.WebSocket, 'prototype', {
      value: originalWebSocket.prototype,
      writable: false
    });
  }

  // 6. Intercept postMessage errors
  const originalPostMessage = window.postMessage;
  window.postMessage = function(message, targetOrigin, transfer) {
    try {
      return originalPostMessage.call(this, message, targetOrigin, transfer);
    } catch (error) {
      if (isExtensionError(error)) {
        logDebug('Advanced interceptor: Extension postMessage error suppressed', error.message);
        return;
      }
      throw error;
    }
  };

  // 7. Monitor and suppress extension-related console errors
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.map(arg => 
      typeof arg === 'object' ? (arg.message || JSON.stringify(arg)) : String(arg)
    ).join(' ');
    
    if (isExtensionError(message)) {
      logDebug('Advanced interceptor: Extension console error suppressed', message);
      return;
    }
    
    originalConsoleError.apply(console, args);
  };

  window.__advancedExtensionInterceptionActive = true;
  logDebug('Advanced extension error interception initialized');
};

/**
 * Create a safe execution environment for critical functions
 */
export const createExtensionSafeEnvironment = (callback) => {
  return async (...args) => {
    try {
      return await callback(...args);
    } catch (error) {
      if (isExtensionError(error)) {
        logDebug('Extension-safe environment: Error suppressed', error.message);
        return null;
      }
      throw error;
    }
  };
};

/**
 * Wrap critical API calls with extension protection
 */
export const protectCriticalAPIs = () => {
  // Protect localStorage
  const originalSetItem = localStorage.setItem;
  localStorage.setItem = function(key, value) {
    try {
      return originalSetItem.call(this, key, value);
    } catch (error) {
      if (isExtensionError(error)) {
        logDebug('Extension interference with localStorage suppressed');
        return;
      }
      throw error;
    }
  };

  // Protect sessionStorage
  const originalSessionSetItem = sessionStorage.setItem;
  sessionStorage.setItem = function(key, value) {
    try {
      return originalSessionSetItem.call(this, key, value);
    } catch (error) {
      if (isExtensionError(error)) {
        logDebug('Extension interference with sessionStorage suppressed');
        return;
      }
      throw error;
    }
  };
};

export default {
  initializeAdvancedExtensionInterception,
  createExtensionSafeEnvironment,
  protectCriticalAPIs
};
