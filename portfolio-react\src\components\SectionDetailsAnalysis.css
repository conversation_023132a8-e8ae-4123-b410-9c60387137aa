.section-details-container {
  min-height: 100vh;
  padding: 50px 30px;
  background:
    linear-gradient(135deg, rgba(75,0,130,0.2) 0%, rgba(255,45,85,0.15) 100%),
    radial-gradient(circle at 20% 20%, rgba(75,0,130,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255,45,85,0.1) 0%, transparent 50%);
  border-radius: 35px;
  box-shadow:
    0 25px 50px rgba(75,0,130,0.25),
    0 15px 35px rgba(255,45,85,0.15),
    0 5px 15px rgba(0,0,0,0.3),
    inset 0 1px 0 rgba(255,255,255,0.1);
  font-family: 'Montserrat', sans-serif;
  color: #fff;
  position: relative;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255,255,255,0.1);
  animation: dashboardFadeIn 0.8s ease-out;
  width: 100%;
  max-width: 1400px;
  margin: 20px auto;
  overflow: hidden;
}

/* Floating particles animation */
.section-details-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255,45,85,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(75,0,130,0.1) 1px, transparent 1px),
    radial-gradient(circle at 50% 10%, rgba(255,255,255,0.05) 1px, transparent 1px),
    radial-gradient(circle at 10% 90%, rgba(255,45,85,0.08) 1px, transparent 1px);
  background-size: 100px 100px, 80px 80px, 120px 120px, 90px 90px;
  animation: floatingParticles 20s linear infinite;
  pointer-events: none;
  z-index: 1;
}

.section-details-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 80% 20%, rgba(75,0,130,0.05) 0%, transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(255,45,85,0.05) 0%, transparent 50%);
  animation: pulseGlow 4s ease-in-out infinite alternate;
  pointer-events: none;
  z-index: 1;
}

@keyframes dashboardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes floatingParticles {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
  100% {
    transform: translateY(0px) rotate(360deg);
  }
}

@keyframes pulseGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

.section-details-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255,255,255,0.05);
  border-radius: 15px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255,255,255,0.1);
  position: relative;
  z-index: 10;
}

.back-to-dashboard-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.back-to-dashboard-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255,45,85,0.3);
}

.section-details-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-details-summary {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(75,0,130,0.2);
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
}

.section-details-loading,
.section-details-error {
  text-align: center;
  padding: 50px 20px;
  font-size: 1.2rem;
  color: #fff;
}

.section-details-error {
  color: #FF2D55;
}

.visitors-analysis-grid {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
  position: relative;
  z-index: 10;
}

.visitor-analysis-card {
  background: rgba(255,255,255,0.05);
  border-radius: 15px;
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(5px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.visitor-analysis-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(75,0,130,0.2);
}

.visitor-header {
  padding: 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  transition: background 0.3s ease;
}

.visitor-header:hover {
  background: rgba(255,255,255,0.05);
}

.visitor-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.visitor-icon {
  font-size: 1.5rem;
  color: #4B0082;
}

.visitor-ip {
  font-size: 1.1rem;
  font-weight: 700;
  color: #FF2D55;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.visitor-total-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.visitor-sections-count {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  color: #4B0082;
  font-weight: 600;
}

.visitor-sections-details {
  padding: 20px;
  background: rgba(255,255,255,0.05);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
  }
}

.visitor-sections-details h4 {
  margin: 0 0 15px 0;
  color: #FF2D55;
  font-size: 1.1rem;
  font-weight: 700;
}

.section-detail-item {
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(255,255,255,0.05);
  border-radius: 10px;
  border-left: 4px solid #FF2D55;
}

.section-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-name {
  font-size: 1rem;
  font-weight: 700;
  color: #fff;
  text-transform: capitalize;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.clickable-section-name {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  color: #FF2D55 !important;
}

.clickable-section-name::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,45,85,0.2), transparent);
  transition: left 0.5s;
}

.clickable-section-name:hover::before {
  left: 100%;
}

.clickable-section-name:hover {
  background: rgba(255,45,85,0.1);
  color: #fff !important;
  transform: scale(1.05);
  box-shadow: 0 2px 10px rgba(255,45,85,0.3);
}

.section-time {
  font-size: 1.1rem;
  font-weight: 700;
  color: #FF2D55;
}

.section-detail-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.section-visits,
.section-avg,
.section-last-visit {
  font-size: 0.85rem;
  color: #fff;
  opacity: 0.8;
  padding: 4px 8px;
  background: rgba(75,0,130,0.3);
  border-radius: 12px;
}

.section-page-urls {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.section-page-urls strong {
  color: #4B0082;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 5px;
}

.page-url {
  font-size: 0.8rem;
  color: #fff;
  opacity: 0.7;
  padding: 2px 8px;
  background: rgba(255,45,85,0.2);
  border-radius: 8px;
  margin: 2px 0;
  display: inline-block;
  margin-right: 5px;
}

.no-data-message {
  text-align: center;
  padding: 60px 20px;
  color: #fff;
  opacity: 0.7;
}

.no-data-message svg {
  font-size: 3rem;
  color: #4B0082;
  margin-bottom: 20px;
}

.no-data-message h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: #FF2D55;
}

.analysis-insights {
  background: rgba(255,255,255,0.05);
  border-radius: 15px;
  padding: 25px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255,255,255,0.1);
  position: relative;
  z-index: 10;
}

.analysis-insights h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  color: #FF2D55;
  font-size: 1.3rem;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.insight-card {
  background: rgba(75,0,130,0.2);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(255,255,255,0.1);
}

.insight-card h4 {
  color: #4B0082;
  font-size: 1rem;
  margin-bottom: 8px;
  font-weight: 700;
}

.insight-card p {
  color: #fff;
  font-size: 0.95rem;
  margin: 0;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-details-container {
    margin: 15px auto;
    padding: 25px 15px;
    border-radius: 20px;
    overflow: hidden;
    max-width: calc(100vw - 30px);
    width: calc(100vw - 30px);
    box-sizing: border-box;
    display: block;
    position: relative;
  }

  .section-details-title {
    font-size: 2rem;
  }

  .visitor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .visitor-info {
    flex-wrap: wrap;
  }

  .section-detail-stats {
    flex-direction: column;
    gap: 8px;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .section-details-container {
    margin: 10px auto;
    padding: 20px 10px;
    border-radius: 15px;
    overflow: hidden;
    max-width: calc(100vw - 20px);
    width: calc(100vw - 20px);
    box-sizing: border-box;
    display: block;
    position: relative;
  }

  .section-details-title {
    font-size: 1.6rem;
    margin-bottom: 20px;
  }

  .section-details-header {
    padding: 15px;
    gap: 15px;
    margin-bottom: 20px;
  }

  .back-to-dashboard-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
    border-radius: 8px;
  }
}
