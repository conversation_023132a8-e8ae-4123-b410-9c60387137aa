#!/usr/bin/env node

// Build script that ensures environment variables are set
console.log('🔧 Setting up environment variables for production build...');

// Set the API URL if not already set
if (!process.env.REACT_APP_API_URL) {
  process.env.REACT_APP_API_URL = 'https://porfolio-pro-backend.onrender.com';
  console.log('✅ REACT_APP_API_URL set to:', process.env.REACT_APP_API_URL);
} else {
  console.log('✅ REACT_APP_API_URL already set to:', process.env.REACT_APP_API_URL);
}

// Set NODE_ENV to production
process.env.NODE_ENV = 'production';
console.log('✅ NODE_ENV set to:', process.env.NODE_ENV);

// Now run the actual build
console.log('🚀 Starting React build...');

const { spawn } = require('child_process');

const buildProcess = spawn('npm', ['run', 'build'], {
  stdio: 'inherit',
  env: process.env,
  shell: true
});

buildProcess.on('close', (code) => {
  if (code === 0) {
    console.log('🎉 Build completed successfully!');
  } else {
    console.error('❌ Build failed with code:', code);
    process.exit(code);
  }
});

buildProcess.on('error', (error) => {
  console.error('❌ Build process error:', error);
  process.exit(1);
});
