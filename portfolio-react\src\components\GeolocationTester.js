import React, { useState } from 'react';
import { batchGetCountriesFromIPs } from '../utils/geolocation';

const GeolocationTester = () => {
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Test IPs that should be detected as Tunisia
  const testIPs = [
    '**************', // Your specific IP
    '**************', // Another 102.157.x.x IP
    '**************', // From your console logs
    '***********',    // Original Tunisia range
    '**********',     // Tunisia North Africa range
    '***********',    // Should be France
    '*******',        // Should be US (Google DNS)
    '127.0.0.1',      // Should be Local
    '999.999.999.999' // Invalid IP for testing
  ];

  const runTest = async () => {
    setLoading(true);
    setError('');
    setTestResults({});

    try {
      console.log('🧪 Starting geolocation test...');
      const results = await batchGetCountriesFromIPs(testIPs);
      
      console.log('🧪 Test results:', results);
      setTestResults(results);
      
      // Log specific results for debugging
      testIPs.forEach(ip => {
        const result = results[ip];
        console.log(`🧪 ${ip} → ${result?.country || 'No result'} ${result?.flag || '❓'}`);
      });
      
    } catch (err) {
      console.error('🧪 Test failed:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getExpectedResult = (ip) => {
    if (ip.startsWith('102.157.') || ip.startsWith('102.169.') || ip.startsWith('41.227.') || ip.startsWith('197.14.')) {
      return 'Tunisia 🇹🇳';
    }
    if (ip.startsWith('176.143.')) return 'France 🇫🇷';
    if (ip === '*******') return 'United States 🇺🇸';
    if (ip === '127.0.0.1') return 'Local 🏠';
    return 'Unknown 🌍';
  };

  const isCorrect = (ip, result) => {
    const expected = getExpectedResult(ip);
    const actual = `${result?.country || 'Unknown'} ${result?.flag || '🌍'}`;
    return expected === actual;
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🧪 Geolocation Testing Tool</h2>
      <p>This tool tests if the IP geolocation fixes are working correctly.</p>
      
      <button 
        onClick={runTest} 
        disabled={loading}
        style={{
          padding: '10px 20px',
          backgroundColor: '#FF2D55',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: loading ? 'not-allowed' : 'pointer',
          marginBottom: '20px'
        }}
      >
        {loading ? 'Testing...' : 'Run Geolocation Test'}
      </button>

      {error && (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          ❌ Error: {error}
        </div>
      )}

      {Object.keys(testResults).length > 0 && (
        <div>
          <h3>📊 Test Results</h3>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f5f5f5' }}>
                <th style={{ padding: '10px', border: '1px solid #ddd' }}>IP Address</th>
                <th style={{ padding: '10px', border: '1px solid #ddd' }}>Expected</th>
                <th style={{ padding: '10px', border: '1px solid #ddd' }}>Actual Result</th>
                <th style={{ padding: '10px', border: '1px solid #ddd' }}>Status</th>
              </tr>
            </thead>
            <tbody>
              {testIPs.map(ip => {
                const result = testResults[ip];
                const correct = isCorrect(ip, result);
                return (
                  <tr key={ip}>
                    <td style={{ padding: '10px', border: '1px solid #ddd', fontFamily: 'monospace' }}>
                      {ip}
                    </td>
                    <td style={{ padding: '10px', border: '1px solid #ddd' }}>
                      {getExpectedResult(ip)}
                    </td>
                    <td style={{ padding: '10px', border: '1px solid #ddd' }}>
                      {result ? `${result.country} ${result.flag}` : 'No result'}
                      {result?.error && <div style={{ fontSize: '12px', color: '#666' }}>
                        Error: {result.errorMessage}
                      </div>}
                    </td>
                    <td style={{ padding: '10px', border: '1px solid #ddd' }}>
                      {result ? (correct ? '✅ Correct' : '❌ Incorrect') : '❓ No data'}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          
          <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f0f8ff', borderRadius: '5px' }}>
            <h4>🎯 Key Test Points:</h4>
            <ul>
              <li><strong>**************</strong> should show <strong>Tunisia 🇹🇳</strong> (your specific IP)</li>
              <li><strong>**************</strong> should show <strong>Tunisia 🇹🇳</strong> (from console logs)</li>
              <li>No IPs should show "Loading..." or get stuck in loading state</li>
              <li>Fallback IPs should show appropriate countries or "Unknown"</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeolocationTester;
