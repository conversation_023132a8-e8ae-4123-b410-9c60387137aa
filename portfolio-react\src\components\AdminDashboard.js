import React, { useEffect, useState } from 'react';
import './AdminDashboard.css';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaChart<PERSON>ie, FaListUl, FaSignOutAlt, FaCrown, FaShieldAlt, FaClock, FaEye, FaBriefcase, FaFolder } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { formatDuration } from '../hooks/useVisitorTracking';
import { logSensitive, logDebug } from '../utils/logger';
import { API_CONFIG } from '../config/apiConfig';
import { safeFetch, isExtensionError } from '../utils/extensionErrorHandler';
import { calculateTodayVisitorsInTunisia, getTimezoneDebugInfo } from '../utils/timezoneHelper';

const AdminDashboard = () => {
  const [stats, setStats] = useState(null);
  const [error, setError] = useState('');
  const [showIpIndex, setShowIpIndex] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchStats = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No token found. Please log in.');
        return;
      }

      // Wake up backend before fetching dashboard data
      await preemptiveWakeup();

      try {
        const response = await safeFetch(API_CONFIG.ENDPOINTS.ADMIN_DASHBOARD, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        // CLIENT-SIDE TIMEZONE FIX: Recalculate today's visitors using Tunisia timezone
        if (data.visits && Array.isArray(data.visits)) {
          const correctedTodayVisitors = calculateTodayVisitorsInTunisia(data.visits);

          // Update the dailyStats with corrected count
          if (data.dailyStats) {
            data.dailyStats.todayVisitors = correctedTodayVisitors;
          }

          // Add debug info
          const debugInfo = getTimezoneDebugInfo();
          logDebug('🕐 Timezone Fix Applied:', {
            originalTodayVisitors: data.dailyStats?.todayVisitors || 0,
            correctedTodayVisitors: correctedTodayVisitors,
            debugInfo: debugInfo
          });
        }

        setStats(data);
      } catch (error) {
        // Don't show errors for extension-related issues
        if (!isExtensionError(error)) {
          logSensitive('Dashboard fetch error:', error);
          setError('Error fetching stats: ' + error.message);
        } else {
          logDebug('Extension error suppressed in AdminDashboard:', error.message);
        }
      }
    };
    fetchStats();
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/');
  };

  const handleSectionDetailsClick = () => {
    navigate('/admin/section-details');
  };

  const handleAllVisitorsClick = () => {
    navigate('/admin/all-visitors');
  };

  const handleExperienceAnalyticsClick = () => {
    navigate('/admin/experience-analytics');
  };

  const handlePortfolioAnalyticsClick = () => {
    navigate('/admin/portfolio-analytics');
  };

  const handleSectionClick = (sectionName) => {
    // Map section names to portfolio URLs
    const sectionUrls = {
      'header': '/#header',
      'intro': '/#intro',
      'intro-crafting': '/#intro-crafting',
      'skills': '/#skills',
      'statistics': '/#statistics',
      'experience': '/#experience',
      'portfolio': '/#portfolio',
      'client-thoughts': '/#client-thoughts',
      'contact': '/#contact',
      'job-detail': '/job/3d-ecommerce-platform-ui-ux-designer'
    };

    const url = sectionUrls[sectionName] || '/';
    const fullUrl = window.location.origin + url;

    // Open in new window
    window.open(fullUrl, '_blank');
  };

  // Touch event handlers for mobile IP display
  const handleTouchStart = (index) => {
    const touchTimer = setTimeout(() => {
      setShowIpIndex(index);
    }, 500); // 500ms long press

    // Store timer to clear it if touch ends early
    window.touchTimer = touchTimer;
  };

  const handleTouchEnd = () => {
    if (window.touchTimer) {
      clearTimeout(window.touchTimer);
    }
    // Hide IP after 3 seconds
    setTimeout(() => {
      setShowIpIndex(null);
    }, 3000);
  };



  if (error) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-error">{error}</div>
        <button onClick={handleLogout} className="dashboard-logout-btn">
          <FaSignOutAlt /> Logout
        </button>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-loading">Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <button onClick={handleLogout} className="dashboard-logout-btn">
        <FaSignOutAlt /> Logout
      </button>

      <h1 className="dashboard-title">
        <FaCrown /> Admin Dashboard <FaShieldAlt />
      </h1>



      <div className="dashboard-stats-grid">
        {/* Total Visitors Card - Clickable */}
        <div
          className="dashboard-stat-card dashboard-card-total clickable-card"
          onClick={handleAllVisitorsClick}
          title="Click to view detailed visitor information"
        >
          <FaUsers className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Total Visitors</div>
          <div className="dashboard-today-views">
            +{stats.dailyStats?.todayVisitors || 0} Today Views
          </div>
          <div className="dashboard-stat-value">{stats.totalVisits || 0}</div>
          <div className="dashboard-stat-subtitle">
            {stats.uniqueVisitors || 0} unique visitors
          </div>
          <div className="dashboard-card-footer">
            <FaEye /> Click for detailed analysis
          </div>
        </div>

        {/* Average Session Duration Card */}
        <div className="dashboard-stat-card dashboard-card-session">
          <FaClock className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Avg Session</div>
          <div className="dashboard-stat-value">
            {stats.summary ? formatDuration(stats.summary.avgSessionDuration) : '0s'}
          </div>
          <div className="dashboard-stat-subtitle">
            Total: {stats.summary ? formatDuration(stats.summary.totalDurationAllSections) : '0s'}
          </div>
        </div>

        {/* Section Statistics Card - Clickable */}
        <div
          className="dashboard-stat-card dashboard-card-section dashboard-section-stats clickable-card"
          onClick={handleSectionDetailsClick}
          title="Click to view detailed section analysis"
        >
          <FaChartPie className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Section Views (by duration)</div>
          <div className="dashboard-section-stats-grid">
            {stats.sectionStats && stats.sectionStats.length > 0 ? (
              stats.sectionStats.slice(0, 5).map((section, index) => (
                <div
                  key={index}
                  className="dashboard-section-stat clickable-section-stat"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSectionClick(section.section);
                  }}
                  title={`Click to visit ${section.section} section`}
                >
                  <span className="section-name">{section.section}</span>
                  <span className="section-duration">
                    {formatDuration(section.totalDuration || 0)}
                  </span>
                  <span className="section-percent">({section.percent}%)</span>
                </div>
              ))
            ) : (
              <div className="dashboard-empty">No section data available</div>
            )}
          </div>
          <div className="dashboard-card-footer">
            <FaEye /> Click for detailed analysis
          </div>
        </div>

        {/* Experience Projects Analytics Card - Clickable */}
        <div
          className="dashboard-stat-card dashboard-card-analytics clickable-card"
          onClick={handleExperienceAnalyticsClick}
          title="Click to view experience projects analytics"
        >
          <FaBriefcase className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Experience Projects</div>
          <div className="dashboard-stat-value">Analytics</div>
          <div className="dashboard-stat-subtitle">
            Job detail page interactions & views
          </div>
          <div className="dashboard-card-footer">
            <FaEye /> Click for detailed analysis
          </div>
        </div>

        {/* Portfolio Projects Analytics Card - Clickable */}
        <div
          className="dashboard-stat-card dashboard-card-analytics clickable-card"
          onClick={handlePortfolioAnalyticsClick}
          title="Click to view portfolio projects analytics"
        >
          <FaFolder className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Portfolio Projects</div>
          <div className="dashboard-stat-value">Analytics</div>
          <div className="dashboard-stat-subtitle">
            Carousel project clicks & interactions
          </div>
          <div className="dashboard-card-footer">
            <FaEye /> Click for detailed analysis
          </div>
        </div>

        {/* Recent Visitors Card - Full Width */}
        <div className="dashboard-stat-card dashboard-card-visitors dashboard-visitor-log">
          <FaListUl className="dashboard-stat-icon" />
          <div className="dashboard-stat-label">Recent Visitors</div>
          <div className="dashboard-visitor-log-list">
            {stats.visits && stats.visits.length > 0 ? (
              <div className="visitor-list">
                {stats.visits.slice(0, 10).map((visit, index) => (
                  <div
                    key={index}
                    className={`visitor-item ${showIpIndex === index ? 'show-ip' : ''}`}
                    onTouchStart={() => handleTouchStart(index)}
                    onTouchEnd={handleTouchEnd}
                    onTouchCancel={handleTouchEnd}
                  >
                    <span className="visitor-section">{visit.section}</span>
                    <span className="visitor-duration">
                      {visit.duration > 0 ? formatDuration(visit.duration) : '-'}
                    </span>
                    <span className="visitor-ip">{visit.ip}</span>
                    <button
                      className="visitor-details-btn"
                      onClick={() => navigate(`/admin/visitor/${encodeURIComponent(visit.ip)}`)}
                    >
                      See Full Details
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="dashboard-empty">No visitor data available</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;