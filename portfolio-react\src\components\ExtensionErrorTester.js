/**
 * Extension Error Tester Component
 * 
 * This component provides a UI to test and monitor extension error handling
 * in real-time. It's useful for debugging and verifying the solution.
 */

import React, { useState, useEffect } from 'react';
import { 
  runExtensionErrorTests, 
  startExtensionErrorMonitoring, 
  generateExtensionErrorReport 
} from '../utils/extensionErrorTest';
import { logUserFriendly } from '../utils/logger';

const ExtensionErrorTester = () => {
  const [testResults, setTestResults] = useState(null);
  const [monitoring, setMonitoring] = useState(false);
  const [report, setReport] = useState(null);
  const [errorCount, setErrorCount] = useState(0);

  useEffect(() => {
    // Generate initial report
    const initialReport = generateExtensionErrorReport();
    setReport(initialReport);
  }, []);

  const handleRunTests = () => {
    logUserFriendly('🧪 Running extension error tests...');
    runExtensionErrorTests();
    setTestResults('Tests running... Check console for results.');
  };

  const handleStartMonitoring = () => {
    if (!monitoring) {
      startExtensionErrorMonitoring();
      setMonitoring(true);
      logUserFriendly('🔍 Extension error monitoring started');
    }
  };

  const handleGenerateReport = () => {
    const newReport = generateExtensionErrorReport();
    setReport(newReport);
    logUserFriendly('📋 Extension error report generated');
  };

  const handleSimulateError = (errorType) => {
    switch (errorType) {
      case 'async-listener':
        setTimeout(() => {
          Promise.reject(new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received'));
        }, 100);
        break;
      
      case 'connection-error':
        setTimeout(() => {
          throw new Error('Could not establish connection. Receiving end does not exist');
        }, 100);
        break;
      
      case 'extension-context':
        setTimeout(() => {
          Promise.reject(new Error('Extension context invalidated'));
        }, 100);
        break;
      
      default:
        logUserFriendly('Unknown error type');
    }
    
    setErrorCount(prev => prev + 1);
    logUserFriendly(`🚨 Simulated extension error #${errorCount + 1}: ${errorType}`);
  };

  return (
    <div style={{ 
      padding: '20px', 
      margin: '20px', 
      border: '2px solid #4B0082', 
      borderRadius: '10px',
      backgroundColor: '#f8f9fa'
    }}>
      <h2 style={{ color: '#4B0082', marginBottom: '20px' }}>
        🛡️ Extension Error Protection Tester
      </h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Protection Status</h3>
        {report && (
          <div style={{ 
            backgroundColor: '#e8f5e8', 
            padding: '10px', 
            borderRadius: '5px',
            marginBottom: '10px'
          }}>
            <p><strong>✅ Basic Handlers:</strong> {report.errorHandlers.customHandlersActive ? 'Active' : 'Inactive'}</p>
            <p><strong>🛡️ Advanced Interception:</strong> {report.errorHandlers.advancedInterceptionActive ? 'Active' : 'Inactive'}</p>
            <p><strong>🔇 Console Filtering:</strong> {report.errorHandlers.consoleFiltersApplied ? 'Active' : 'Inactive'}</p>
            <p><strong>🌐 Extensions Detected:</strong> {Object.values(report.extensions).some(Boolean) ? 'Yes' : 'No'}</p>
          </div>
        )}
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Test Controls</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={handleRunTests}
            style={{
              padding: '10px 15px',
              backgroundColor: '#4B0082',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            🧪 Run All Tests
          </button>
          
          <button 
            onClick={handleStartMonitoring}
            disabled={monitoring}
            style={{
              padding: '10px 15px',
              backgroundColor: monitoring ? '#6c757d' : '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: monitoring ? 'not-allowed' : 'pointer'
            }}
          >
            {monitoring ? '🔍 Monitoring Active' : '🔍 Start Monitoring'}
          </button>
          
          <button 
            onClick={handleGenerateReport}
            style={{
              padding: '10px 15px',
              backgroundColor: '#17a2b8',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            📋 Generate Report
          </button>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Simulate Extension Errors</h3>
        <p style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>
          These buttons simulate common extension errors. If protection is working, 
          these errors should be suppressed and not appear in the console.
        </p>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={() => handleSimulateError('async-listener')}
            style={{
              padding: '8px 12px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🚨 Async Listener Error
          </button>
          
          <button 
            onClick={() => handleSimulateError('connection-error')}
            style={{
              padding: '8px 12px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🚨 Connection Error
          </button>
          
          <button 
            onClick={() => handleSimulateError('extension-context')}
            style={{
              padding: '8px 12px',
              backgroundColor: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🚨 Context Error
          </button>
        </div>
        <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
          Errors simulated: {errorCount}
        </p>
      </div>

      {testResults && (
        <div style={{ 
          backgroundColor: '#fff3cd', 
          padding: '10px', 
          borderRadius: '5px',
          marginTop: '10px'
        }}>
          <h4>Test Results:</h4>
          <p>{testResults}</p>
        </div>
      )}

      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <p><strong>Instructions:</strong></p>
        <ol>
          <li>Click "Run All Tests" to verify all protection layers are working</li>
          <li>Click "Start Monitoring" to track extension errors in real-time</li>
          <li>Use the "Simulate" buttons to test error suppression</li>
          <li>Check the browser console - suppressed errors should not appear</li>
          <li>Look for debug messages confirming errors were caught and suppressed</li>
        </ol>
      </div>
    </div>
  );
};

export default ExtensionErrorTester;
