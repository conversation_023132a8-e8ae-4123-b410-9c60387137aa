/* Admin Dashboard Styles - Enhanced Portfolio Theme */
.dashboard-container {
  max-width: 1400px;
  margin: 20px auto;
  padding: 50px 30px;
  background:
    linear-gradient(135deg, rgba(75,0,130,0.2) 0%, rgba(255,45,85,0.15) 100%),
    radial-gradient(circle at 20% 20%, rgba(75,0,130,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255,45,85,0.1) 0%, transparent 50%);
  border-radius: 35px;
  box-shadow:
    0 25px 50px rgba(75,0,130,0.25),
    0 15px 35px rgba(255,45,85,0.15),
    0 5px 15px rgba(0,0,0,0.3),
    inset 0 1px 0 rgba(255,255,255,0.1);
  font-family: 'Montserrat', sans-serif;
  color: #fff;
  position: relative;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255,255,255,0.1);
  min-height: 90vh;
  animation: dashboardFadeIn 0.8s ease-out;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

@keyframes dashboardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Add floating particles effect */
.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255,45,85,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(75,0,130,0.1) 1px, transparent 1px);
  background-size: 50px 50px, 30px 30px;
  animation: floatingParticles 20s linear infinite;
  pointer-events: none;
  border-radius: 35px;
}

@keyframes floatingParticles {
  0% { transform: translateY(0px) rotate(0deg); }
  100% { transform: translateY(-20px) rotate(360deg); }
}

.dashboard-title {
  font-size: 3rem;
  font-weight: 900;
  letter-spacing: 3px;
  margin-bottom: 50px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 50%, #4B0082 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  text-transform: uppercase;
  text-align: center;
  position: relative;
  padding: 20px 0;
}

.dashboard-title::before,
.dashboard-title::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 100px;
  height: 3px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  border-radius: 2px;
}

.dashboard-title::before {
  left: 0;
  transform: translateY(-50%);
}

.dashboard-title::after {
  right: 0;
  transform: translateY(-50%);
}

.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 40px;
  margin-top: 30px;
  width: 100%;
  box-sizing: border-box;
}

/* Staggered animation for stat cards */
.dashboard-stat-card:nth-child(1) {
  animation: cardSlideIn 0.6s ease-out 0.2s both;
}

.dashboard-stat-card:nth-child(2) {
  animation: cardSlideIn 0.6s ease-out 0.4s both;
}

.dashboard-stat-card:nth-child(3) {
  animation: cardSlideIn 0.6s ease-out 0.6s both;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dashboard-stat-card {
  background:
    linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(75,0,130,0.1) 100%),
    rgba(255,255,255,0.05);
  border-radius: 25px;
  box-shadow:
    0 15px 35px rgba(75,0,130,0.2),
    0 8px 20px rgba(255,45,85,0.15),
    0 3px 10px rgba(0,0,0,0.3),
    inset 0 1px 0 rgba(255,255,255,0.15);
  padding: 40px 35px 35px 35px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-height: 280px;
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255,255,255,0.15);
  backdrop-filter: blur(15px);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dashboard-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  border-radius: 25px 25px 0 0;
}

.dashboard-stat-card:hover {
  box-shadow:
    0 25px 50px rgba(75,0,130,0.35),
    0 15px 30px rgba(255,45,85,0.25),
    0 5px 15px rgba(0,0,0,0.4),
    inset 0 1px 0 rgba(255,255,255,0.2);
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(255,255,255,0.25);
}

.dashboard-stat-icon {
  font-size: 2.8rem;
  margin-bottom: 25px;
  opacity: 0.9;
  padding: 15px;
  background: rgba(255,255,255,0.1);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.dashboard-stat-card:hover .dashboard-stat-icon {
  transform: scale(1.1) rotate(5deg);
  background: rgba(255,255,255,0.15);
}

.dashboard-stat-label {
  font-size: 1.2rem;
  font-weight: 800;
  color: #FF2D55;
  margin-bottom: 15px;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  position: relative;
  padding-bottom: 8px;
}

.dashboard-stat-label::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  border-radius: 1px;
}

.dashboard-stat-value {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  margin-bottom: 0;
  letter-spacing: 3px;
  text-shadow: 0 2px 4px rgba(75,0,130,0.3);
  transition: all 0.3s ease;
}

.dashboard-stat-card:hover .dashboard-stat-value {
  transform: scale(1.05);
}

.dashboard-today-views {
  font-size: 1.1rem;
  color: #FF2D55;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 0 10px rgba(255, 45, 85, 0.5);
  background: linear-gradient(135deg, #FF2D55 0%, #FF6B9D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  letter-spacing: 1px;
  animation: todayViewsPulse 2s ease-in-out infinite;
}

@keyframes todayViewsPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.dashboard-stat-subtitle {
  font-size: 0.9rem;
  color: #fff;
  opacity: 0.7;
  margin-top: 4px;
  font-weight: 500;
}

.clickable-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(75,0,130,0.3);
}

.dashboard-card-footer {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid rgba(255,255,255,0.1);
  font-size: 0.85rem;
  color: #FF2D55;
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0.8;
}

.section-duration {
  color: #4B0082;
  font-weight: 700;
  font-size: 0.9rem;
}

.dashboard-section-stats {
  min-height: 220px;
}

.dashboard-section-stats-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}

.dashboard-section-stat {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 1.1rem;
  background: rgba(75,0,130,0.08);
  border-radius: 12px;
  padding: 10px 16px;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(75,0,130,0.05);
  transition: all 0.3s ease;
}

.clickable-section-stat {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.clickable-section-stat::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,45,85,0.1), transparent);
  transition: left 0.5s;
}

.clickable-section-stat:hover::before {
  left: 100%;
}

.clickable-section-stat:hover {
  background: rgba(255,45,85,0.1);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(255,45,85,0.2);
  border-left: 3px solid #FF2D55;
}
.section-name {
  color: #FF2D55;
  font-weight: 700;
  min-width: 90px;
  text-transform: capitalize;
}
.section-count {
  color: #4B0082;
  font-weight: 700;
}
.section-percent {
  color: #fff;
  font-size: 0.98rem;
  opacity: 0.8;
}

/* Analytics Cards Specific Styles */
.dashboard-card-analytics {
  background:
    linear-gradient(135deg, rgba(75,0,130,0.15) 0%, rgba(255,45,85,0.1) 100%),
    rgba(255,255,255,0.05);
  border: 2px solid rgba(255,215,0,0.2);
  position: relative;
  overflow: hidden;
}

.dashboard-card-analytics::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border-radius: 25px 25px 0 0;
}

.dashboard-card-analytics:hover {
  border-color: rgba(255,215,0,0.4);
  box-shadow:
    0 25px 50px rgba(255,215,0,0.2),
    0 15px 30px rgba(75,0,130,0.25),
    0 5px 15px rgba(0,0,0,0.4),
    inset 0 1px 0 rgba(255,255,255,0.2);
  transform: translateY(-8px) scale(1.02);
}

.dashboard-card-analytics .dashboard-stat-icon {
  color: #FFD700;
  background: rgba(255,215,0,0.1);
  border: 2px solid rgba(255,215,0,0.2);
}

.dashboard-card-analytics:hover .dashboard-stat-icon {
  background: rgba(255,215,0,0.2);
  border-color: rgba(255,215,0,0.4);
  color: #FFA500;
  transform: scale(1.1) rotate(5deg);
}

.dashboard-card-analytics .dashboard-stat-label {
  color: #FFD700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.dashboard-card-analytics .dashboard-stat-value {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.dashboard-card-analytics .dashboard-stat-subtitle {
  color: rgba(255,215,0,0.8);
}

.dashboard-card-analytics .dashboard-card-footer {
  color: rgba(255,215,0,0.9);
  border-top: 1px solid rgba(255,215,0,0.2);
}

/* Full-width visitor log card */
.dashboard-card-visitors.dashboard-visitor-log {
  grid-column: 1 / -1; /* Span all columns */
  min-height: 220px;
  width: 100%;
}

.dashboard-visitor-log {
  min-height: 220px;
  width: 100%;
}
.dashboard-visitor-log-list {
  margin-top: 10px;
  max-height: 180px;
  overflow-y: auto;
  width: 100%;
  border-radius: 10px;
  padding: 8px 0 8px 0;
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(255,45,85,0.5) rgba(255,255,255,0.1);
}

/* Webkit scrollbar styling */
.dashboard-visitor-log-list::-webkit-scrollbar {
  width: 6px;
}

.dashboard-visitor-log-list::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.05);
  border-radius: 3px;
}

.dashboard-visitor-log-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.dashboard-visitor-log-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
}
.visitor-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 12px;
  margin: 0;
}
.visitor-item {
  display: grid;
  grid-template-columns: 1.5fr 0.8fr 1fr 1.2fr;
  gap: 15px;
  align-items: center;
  font-size: 1rem;
  padding: 12px 15px;
  border-radius: 8px;
  border: 1px solid rgba(255,255,255,0.1);
  transition: all 0.3s ease;
  position: relative;
}
.visitor-item:hover {
  background: rgba(255,255,255,0.1);
  border-color: rgba(255,255,255,0.2);
  transform: translateY(-1px);
}
.visitor-section {
  color: #fff;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
  min-width: 80px;
}
.visitor-duration {
  color: #FF2D55;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 50px;
  opacity: 0.9;
  text-align: center;
}
.visitor-ip {
  color: #FF2D55;
  font-size: 0.85rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  transition: opacity 0.3s ease;
}
.visitor-details-btn {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.8rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow:
    0 4px 15px rgba(255,45,85,0.3),
    0 2px 8px rgba(75,0,130,0.2),
    inset 0 1px 0 rgba(255,255,255,0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}
.visitor-details-btn:hover {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  box-shadow:
    0 8px 25px rgba(255,45,85,0.4),
    0 4px 15px rgba(75,0,130,0.3),
    inset 0 1px 0 rgba(255,255,255,0.2);
  transform: translateY(-2px) scale(1.05);
  border-color: rgba(255,255,255,0.25);
}
.dashboard-empty {
  color: #fff;
  opacity: 0.7;
  font-style: italic;
  padding: 20px 0;
  text-align: center;
}
.dashboard-error {
  color: #FF2D55;
  background: rgba(75,0,130,0.12);
  border-radius: 12px;
  padding: 18px 30px;
  margin: 40px auto;
  max-width: 600px;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
}
.dashboard-loading {
  color: #4B0082;
  background: rgba(255,255,255,0.08);
  border-radius: 12px;
  padding: 18px 30px;
  margin: 40px auto;
  max-width: 600px;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
}

.dashboard-logout-btn {
  position: absolute;
  top: 25px;
  right: 25px;
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-size: 1rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow:
    0 6px 20px rgba(255,45,85,0.3),
    0 3px 10px rgba(75,0,130,0.2),
    inset 0 1px 0 rgba(255,255,255,0.15);
  cursor: pointer;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.dashboard-logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.dashboard-logout-btn:hover::before {
  left: 100%;
}

.dashboard-logout-btn:hover {
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  color: #fff;
  transform: translateY(-2px) scale(1.03);
  box-shadow:
    0 10px 25px rgba(255,45,85,0.4),
    0 5px 15px rgba(75,0,130,0.3),
    inset 0 1px 0 rgba(255,255,255,0.2);
  border-color: rgba(255,255,255,0.25);
}
/* Tablet Responsiveness */
@media (max-width: 1024px) {
  .dashboard-container {
    max-width: 95%;
    margin: 15px auto;
    padding: 40px 25px;
  }

  .dashboard-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
  }

  .dashboard-title {
    font-size: 2.5rem;
    margin-bottom: 40px;
  }

  .dashboard-title::before,
  .dashboard-title::after {
    width: 60px;
  }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .dashboard-container {
    margin: 20px auto;
    padding: 30px 20px;
    border-radius: 25px;
    min-height: auto;
    overflow: hidden;
    max-width: calc(100vw - 40px);
    width: calc(100vw - 40px);
    box-sizing: border-box;
    display: block;
    position: relative;
  }

  .dashboard-title {
    font-size: 2rem;
    margin-bottom: 30px;
    flex-direction: column;
    gap: 10px;
  }

  .dashboard-title::before,
  .dashboard-title::after {
    display: none;
  }

  .dashboard-stats-grid {
    grid-template-columns: 1fr;
    gap: 25px;
    margin-top: 20px;
  }

  .dashboard-stat-card {
    min-height: 200px;
    padding: 25px 20px;
    border-radius: 20px;
  }

  .dashboard-stat-icon {
    font-size: 2.2rem;
    padding: 12px;
    margin-bottom: 15px;
  }

  .dashboard-stat-label {
    font-size: 1rem;
    margin-bottom: 10px;
  }

  .dashboard-stat-value {
    font-size: 2.5rem;
    letter-spacing: 1px;
  }

  .dashboard-logout-btn {
    top: 15px;
    right: 15px;
    padding: 10px 16px;
    font-size: 0.9rem;
    border-radius: 20px;
    gap: 6px;
  }
}

/* Small Mobile Responsiveness */
@media (max-width: 480px) {
  .dashboard-container {
    margin: 15px auto;
    padding: 25px 15px;
    border-radius: 20px;
    overflow: hidden;
    max-width: calc(100vw - 30px);
    width: calc(100vw - 30px);
    box-sizing: border-box;
    display: block;
    position: relative;
  }

  .dashboard-title {
    font-size: 1.6rem;
    margin-bottom: 25px;
  }

  .dashboard-stats-grid {
    gap: 20px;
  }

  .dashboard-stat-card {
    min-height: 180px;
    padding: 20px 15px;
    border-radius: 15px;
  }

  .dashboard-stat-icon {
    font-size: 2rem;
    padding: 10px;
    margin-bottom: 12px;
  }

  .dashboard-stat-label {
    font-size: 0.9rem;
    letter-spacing: 0.5px;
  }

  .dashboard-stat-value {
    font-size: 2.2rem;
  }

  .dashboard-logout-btn {
    top: 12px;
    right: 12px;
    padding: 8px 12px;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
  }

  /* Improve section stats for mobile */
  .dashboard-section-stats-grid {
    gap: 8px;
  }

  .dashboard-section-stat {
    font-size: 0.95rem;
    padding: 8px 12px;
    border-radius: 8px;
  }

  .section-name {
    min-width: 70px;
    font-size: 0.9rem;
  }

  /* Improve visitor log for mobile */
  .visitor-item {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 15px;
    cursor: pointer;
  }

  .visitor-ip {
    opacity: 0;
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.8);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    pointer-events: none;
    z-index: 10;
  }

  .visitor-item:hover .visitor-ip,
  .visitor-item:active .visitor-ip,
  .visitor-item.show-ip .visitor-ip {
    opacity: 1;
  }

  .visitor-details-btn {
    font-size: 0.75rem;
    padding: 6px 12px;
    margin-top: 8px;
    border-radius: 15px;
    letter-spacing: 0.5px;
  }

  .visitor-section {
    font-size: 0.85rem;
  }
}



/* Extra Small Mobile (320px and below) */
@media (max-width: 320px) {
  .dashboard-container {
    margin: 10px auto;
    padding: 20px 10px;
    border-radius: 15px;
    overflow: hidden;
    max-width: calc(100vw - 20px);
    width: calc(100vw - 20px);
    box-sizing: border-box;
    display: block;
    position: relative;
  }

  .dashboard-title {
    font-size: 1.4rem;
    margin-bottom: 20px;
  }

  .dashboard-stats-grid {
    gap: 15px;
    grid-template-columns: 1fr;
  }

  .dashboard-stat-card {
    min-height: 160px;
    padding: 15px 10px;
    border-radius: 12px;
  }

  .dashboard-stat-icon {
    font-size: 1.8rem;
    padding: 8px;
    margin-bottom: 10px;
  }

  .dashboard-stat-label {
    font-size: 0.8rem;
    letter-spacing: 0.3px;
  }

  .dashboard-stat-value {
    font-size: 2rem;
  }

  .dashboard-logout-btn {
    top: 8px;
    right: 8px;
    padding: 6px 10px;
    font-size: 0.7rem;
    border-radius: 15px;
  }
}