/* Scroll to <PERSON> Button */
.scroll-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease-in-out;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.scroll-to-top-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF2D55, #4B0082);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 4px 15px rgba(255, 45, 85, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

.scroll-to-top-button:hover {
  transform: translateY(-3px) scale(1.1);
  background: linear-gradient(135deg, #FF2D55, #6A0DAD);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 
    0 6px 20px rgba(255, 45, 85, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.3);
}

.scroll-to-top-button:active {
  transform: translateY(-1px) scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-to-top {
    bottom: 20px;
    right: 20px;
  }
  
  .scroll-to-top-button {
    width: 45px;
    height: 45px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .scroll-to-top {
    bottom: 15px;
    right: 15px;
  }
  
  .scroll-to-top-button {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
}
