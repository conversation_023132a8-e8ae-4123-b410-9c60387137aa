/**
 * Test Script for Extension Error Handling
 * 
 * This script demonstrates how the extension error handling works
 * and can be used to verify the fix is working correctly.
 */

import { 
  isExtensionError, 
  safeExecute, 
  extensionSafePromise,
  initializeExtensionErrorHandling 
} from './extensionErrorHandler';

/**
 * Simulate the specific error that was occurring
 */
export const simulateExtensionError = () => {
  const error = new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
  
  console.log('Testing extension error detection:');
  console.log('Error message:', error.message);
  console.log('Is extension error:', isExtensionError(error));
  
  return error;
};

/**
 * Test safe execution with extension error
 */
export const testSafeExecution = async () => {
  console.log('\n--- Testing Safe Execution ---');
  
  // Test 1: Function that throws extension error
  const extensionErrorFunction = async () => {
    throw new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
  };
  
  const result1 = await safeExecute(extensionErrorFunction, 'extension error test');
  console.log('Extension error result (should be null):', result1);
  
  // Test 2: Function that throws regular error
  const regularErrorFunction = async () => {
    throw new Error('Network timeout');
  };
  
  try {
    await safeExecute(regularErrorFunction, 'regular error test');
  } catch (error) {
    console.log('Regular error caught (should be thrown):', error.message);
  }
  
  // Test 3: Successful function
  const successFunction = async () => {
    return 'Success!';
  };
  
  const result3 = await safeExecute(successFunction, 'success test');
  console.log('Success result:', result3);
};

/**
 * Test promise wrapper
 */
export const testPromiseWrapper = async () => {
  console.log('\n--- Testing Promise Wrapper ---');
  
  // Test 1: Promise that rejects with extension error
  const extensionErrorPromise = Promise.reject(
    new Error('Extension context invalidated')
  );
  
  const result1 = await extensionSafePromise(extensionErrorPromise, 'fallback');
  console.log('Extension error promise result (should be fallback):', result1);
  
  // Test 2: Promise that rejects with regular error
  const regularErrorPromise = Promise.reject(new Error('API error'));
  
  try {
    await extensionSafePromise(regularErrorPromise);
  } catch (error) {
    console.log('Regular error promise caught (should be thrown):', error.message);
  }
  
  // Test 3: Successful promise
  const successPromise = Promise.resolve('Promise success!');
  const result3 = await extensionSafePromise(successPromise);
  console.log('Success promise result:', result3);
};

/**
 * Test global error handling
 */
export const testGlobalErrorHandling = () => {
  console.log('\n--- Testing Global Error Handling ---');
  
  // Initialize error handling
  initializeExtensionErrorHandling();
  console.log('Global error handlers initialized');
  
  // Simulate extension error (this should be caught and suppressed)
  setTimeout(() => {
    const error = new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received');
    console.log('Throwing extension error (should be suppressed)...');
    throw error;
  }, 100);
  
  // Simulate regular error (this should still appear)
  setTimeout(() => {
    console.log('Throwing regular error (should appear in console)...');
    throw new Error('This is a regular application error');
  }, 200);
};

/**
 * Run all tests
 */
export const runAllTests = async () => {
  console.log('🧪 Running Extension Error Handling Tests\n');
  
  try {
    simulateExtensionError();
    await testSafeExecution();
    await testPromiseWrapper();
    testGlobalErrorHandling();
    
    console.log('\n✅ All tests completed successfully!');
    console.log('Check the console for any errors - extension errors should be suppressed.');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testExtensionErrorHandling = {
    simulateExtensionError,
    testSafeExecution,
    testPromiseWrapper,
    testGlobalErrorHandling,
    runAllTests
  };
}

export default {
  simulateExtensionError,
  testSafeExecution,
  testPromiseWrapper,
  testGlobalErrorHandling,
  runAllTests
};
