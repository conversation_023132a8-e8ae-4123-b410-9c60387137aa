import React, { useEffect } from 'react';
import './LockedJobNotification.css';

const LockedJobNotification = ({ isOpen, onClose, jobTitle }) => {
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="locked-job-overlay" onClick={onClose}>
      <div className="locked-job-content" onClick={(e) => e.stopPropagation()}>
        <div className="locked-job-icon">
          <span>🔒</span>
        </div>
        <h2 className="locked-job-title">App Available Soon</h2>
        <p className="locked-job-message">
          <strong>{jobTitle}</strong> is currently under development. 
          We're working hard to bring you an amazing experience with cutting-edge features and exceptional performance.
        </p>
        <div className="locked-job-details">
          <p className="locked-job-description">
            An intelligent Tunisian web application that leverages AI to scan medicine packaging, accurately identify medication names, and provide users with detailed information about each product — including whether it is a medication or a dietary supplement. It also helps users understand its purpose and functionality, and shows exactly where the product is available in Tunisia.
          </p>
        </div>
        <div className="locked-job-contact">
          <p>Stay tuned for updates!</p>
          <button className="locked-job-close" onClick={onClose}>
            Got it!
          </button>
        </div>
      </div>
    </div>
  );
};

export default LockedJobNotification;
