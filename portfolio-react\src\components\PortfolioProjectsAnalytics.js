import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Fa<PERSON>rrow<PERSON><PERSON>t, FaFolder, FaMousePointer, FaClock, FaUsers, FaChartLine, FaExternalLinkAlt, FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';
import './PortfolioProjectsAnalytics.css';
import { preemptiveWakeup } from '../utils/backendWakeup';
import { formatDuration } from '../hooks/useVisitorTracking';
import { API_CONFIG } from '../config/apiConfig';
import { safeFetch, isExtensionError } from '../utils/extensionErrorHandler';

const PortfolioProjectsAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'available', 'unavailable'
  const navigate = useNavigate();

  useEffect(() => {
    fetchAnalytics();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError('');

      // Wake up backend before attempting to fetch analytics
      console.log('Waking up backend...');
      await preemptiveWakeup();

      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/admin/login');
        return;
      }

      console.log('Fetching portfolio analytics from:', API_CONFIG.ENDPOINTS.PORTFOLIO_PROJECTS_ANALYTICS);

      const response = await safeFetch(API_CONFIG.ENDPOINTS.PORTFOLIO_PROJECTS_ANALYTICS, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);

      if (response.status === 401) {
        localStorage.removeItem('token');
        navigate('/admin/login');
        return;
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`Failed to fetch analytics (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      console.log('Analytics data received:', data);
      setAnalytics(data);
    } catch (err) {
      // Don't show errors for extension-related issues
      if (!isExtensionError(err)) {
        console.error('Analytics fetch error:', err);
        setError(`Error loading analytics: ${err.message}`);
      } else {
        console.log('Extension error suppressed in PortfolioProjectsAnalytics:', err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleProjectClick = (projectUrl) => {
    if (projectUrl && projectUrl !== '' && projectUrl !== '#') {
      window.open(projectUrl, '_blank');
    }
  };

  const handleBackClick = () => {
    navigate('/admin/dashboard');
  };

  const getDisplayData = () => {
    if (!analytics) return [];
    
    switch (activeTab) {
      case 'available':
        return analytics.availableProjects || [];
      case 'unavailable':
        return analytics.unavailableProjects || [];
      default:
        return analytics.data || [];
    }
  };

  if (loading) {
    return (
      <div className="portfolio-analytics-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading Portfolio Analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="portfolio-analytics-container">
        <div className="error-message">
          <h3>Error Loading Analytics</h3>
          <p>{error}</p>
          <button onClick={fetchAnalytics} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio-analytics-container">
      <div className="analytics-header">
        <button onClick={handleBackClick} className="back-button">
          <FaArrowLeft /> Back to Dashboard
        </button>
        <h1><FaFolder /> Portfolio Projects Analytics</h1>
        <p>Detailed analytics for portfolio carousel project interactions</p>
      </div>

      {analytics && (
        <>
          {/* Summary Cards */}
          <div className="analytics-summary">
            <div className="summary-card">
              <FaFolder className="summary-icon" />
              <div className="summary-content">
                <h3>Total Projects</h3>
                <div className="summary-value">{analytics.totalProjects}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaMousePointer className="summary-icon" />
              <div className="summary-content">
                <h3>Total Interactions</h3>
                <div className="summary-value">{analytics.summary.totalInteractions}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaClock className="summary-icon" />
              <div className="summary-content">
                <h3>Total Time</h3>
                <div className="summary-value">{formatDuration(analytics.summary.totalDuration)}</div>
              </div>
            </div>
            <div className="summary-card">
              <FaChartLine className="summary-icon" />
              <div className="summary-content">
                <h3>Avg Interactions/Project</h3>
                <div className="summary-value">{analytics.summary.avgInteractionsPerProject}</div>
              </div>
            </div>
          </div>

          {/* Availability Summary */}
          <div className="availability-summary">
            <div className="availability-card available">
              <FaCheckCircle className="availability-icon" />
              <div className="availability-content">
                <h3>Available Projects</h3>
                <div className="availability-value">{analytics.summary.availableProjectsCount}</div>
              </div>
            </div>
            <div className="availability-card unavailable">
              <FaExclamationTriangle className="availability-icon" />
              <div className="availability-content">
                <h3>Unavailable Projects</h3>
                <div className="availability-value">{analytics.summary.unavailableProjectsCount}</div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="tab-navigation">
            <button 
              className={`tab-button ${activeTab === 'all' ? 'active' : ''}`}
              onClick={() => setActiveTab('all')}
            >
              All Projects ({analytics.totalProjects})
            </button>
            <button 
              className={`tab-button ${activeTab === 'available' ? 'active' : ''}`}
              onClick={() => setActiveTab('available')}
            >
              Available ({analytics.summary.availableProjectsCount})
            </button>
            <button 
              className={`tab-button ${activeTab === 'unavailable' ? 'active' : ''}`}
              onClick={() => setActiveTab('unavailable')}
            >
              Unavailable ({analytics.summary.unavailableProjectsCount})
            </button>
          </div>

          {/* Projects List */}
          <div className="projects-analytics-list">
            {getDisplayData().length > 0 ? (
              <div className="projects-grid">
                {getDisplayData().map((project, index) => (
                  <div key={index} className={`project-analytics-card ${project.availabilityStatus === false ? 'unavailable' : 'available'}`}>
                    <div className="project-header">
                      <h3 className="project-title">{project.projectTitle}</h3>
                      <div className="project-status">
                        {project.availabilityStatus !== false ? (
                          <span className="status-badge available">
                            <FaCheckCircle /> Available
                          </span>
                        ) : (
                          <span className="status-badge unavailable">
                            <FaExclamationTriangle /> Unavailable
                          </span>
                        )}
                      </div>
                      {project.projectUrl && project.projectUrl !== '' && project.projectUrl !== '#' && (
                        <button 
                          className="view-project-btn"
                          onClick={() => handleProjectClick(project.projectUrl)}
                        >
                          <FaExternalLinkAlt /> View Project
                        </button>
                      )}
                    </div>
                    
                    <div className="project-stats">
                      <div className="stat-item">
                        <FaMousePointer className="stat-icon" />
                        <span className="stat-label">Interactions:</span>
                        <span className="stat-value">{project.totalInteractions}</span>
                      </div>
                      <div className="stat-item">
                        <FaClock className="stat-icon" />
                        <span className="stat-label">Duration:</span>
                        <span className="stat-value">{formatDuration(project.totalDuration)}</span>
                      </div>
                      <div className="stat-item">
                        <FaUsers className="stat-icon" />
                        <span className="stat-label">Unique Visitors:</span>
                        <span className="stat-value">{project.uniqueVisitorCount}</span>
                      </div>
                      <div className="stat-item">
                        <FaClock className="stat-icon" />
                        <span className="stat-label">Avg Duration:</span>
                        <span className="stat-value">{formatDuration(project.avgDuration)}</span>
                      </div>
                    </div>

                    <div className="project-id">
                      <strong>Project ID:</strong> {project.projectId || 'N/A'}
                    </div>

                    {project.interactionTypes && project.interactionTypes.length > 0 && (
                      <div className="interaction-types">
                        <strong>Interaction Types:</strong>
                        <div className="types-list">
                          {project.interactionTypes.map((type, idx) => (
                            <span key={idx} className="interaction-type-badge">{type}</span>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="last-interaction">
                      <strong>Last Interaction:</strong> {
                        project.lastInteraction ? 
                        new Date(project.lastInteraction).toLocaleString() : 
                        'Never'
                      }
                    </div>

                    {project.recentInteractions && project.recentInteractions.length > 0 && (
                      <div className="recent-interactions">
                        <h4>Recent Interactions</h4>
                        <div className="interactions-list">
                          {project.recentInteractions.slice(0, 3).map((interaction, idx) => (
                            <div key={idx} className="interaction-item">
                              <span className="interaction-ip">{interaction.ip}</span>
                              <span className="interaction-type">{interaction.interactionType}</span>
                              <span className="interaction-time">
                                {new Date(interaction.timestamp).toLocaleString()}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">
                <p>No portfolio project analytics data available for this category.</p>
                <p>Data will appear once users start interacting with portfolio projects.</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default PortfolioProjectsAnalytics;
