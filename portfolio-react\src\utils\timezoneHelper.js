/**
 * Timezone Helper Utilities
 * Helps handle timezone differences between server (UTC) and user (Tunisia)
 */

/**
 * Calculate if a timestamp should be considered "today" in Tunisia timezone
 * @param {string|Date} timestamp - The timestamp to check
 * @returns {boolean} - True if the timestamp is "today" in Tunisia
 */
export const isTodayInTunisia = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  
  // Get today's date in Tunisia timezone
  const tunisiaToday = new Date().toLocaleDateString('en-CA', { 
    timeZone: 'Africa/Tunis' 
  }); // YYYY-MM-DD format
  
  // Get the timestamp's date in Tunisia timezone
  const timestampTunisiaDate = date.toLocaleDateString('en-CA', { 
    timeZone: 'Africa/Tunis' 
  });
  
  return tunisiaToday === timestampTunisiaDate;
};

/**
 * Get Tunisia timezone offset in hours
 * @returns {number} - Offset in hours (usually +1)
 */
export const getTunisiaOffset = () => {
  const now = new Date();
  const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
  const tunisiaTime = new Date(utcTime + (1 * 3600000)); // Tunisia is UTC+1
  return 1; // Tunisia is UTC+1 (may be UTC+2 during DST)
};

/**
 * Convert UTC timestamp to Tunisia timezone
 * @param {string|Date} utcTimestamp - UTC timestamp
 * @returns {Date} - Date in Tunisia timezone
 */
export const convertToTunisiaTime = (utcTimestamp) => {
  const date = new Date(utcTimestamp);
  return new Date(date.toLocaleString('en-US', { timeZone: 'Africa/Tunis' }));
};

/**
 * Get today's date range in Tunisia timezone (for server queries)
 * @returns {Object} - {start: Date, end: Date} representing today in Tunisia
 */
export const getTodayRangeInTunisia = () => {
  const now = new Date();
  const tunisiaDateString = now.toLocaleDateString('en-CA', { 
    timeZone: 'Africa/Tunis' 
  });
  
  const start = new Date(tunisiaDateString + 'T00:00:00.000Z');
  const end = new Date(start);
  end.setDate(end.getDate() + 1);
  
  return { start, end };
};

/**
 * Debug timezone information
 * @returns {Object} - Debug info about current timezone situation
 */
export const getTimezoneDebugInfo = () => {
  const now = new Date();
  const utcTime = now.toISOString();
  const tunisiaTime = now.toLocaleString('en-US', { timeZone: 'Africa/Tunis' });
  const tunisiaDate = now.toLocaleDateString('en-CA', { timeZone: 'Africa/Tunis' });
  const { start, end } = getTodayRangeInTunisia();
  
  return {
    serverTime: utcTime,
    tunisiaTime: tunisiaTime,
    tunisiaDate: tunisiaDate,
    todayRange: {
      start: start.toISOString(),
      end: end.toISOString()
    },
    timezoneOffset: getTunisiaOffset()
  };
};

/**
 * Client-side fix for today visitors count
 * This recalculates today's visitors using Tunisia timezone
 * @param {Array} visits - Array of visit objects with timestamp and ip
 * @returns {number} - Count of unique visitors today in Tunisia timezone
 */
export const calculateTodayVisitorsInTunisia = (visits) => {
  if (!visits || !Array.isArray(visits)) return 0;
  
  const todayVisitors = new Set();
  
  visits.forEach(visit => {
    if (visit.timestamp && visit.ip && isTodayInTunisia(visit.timestamp)) {
      todayVisitors.add(visit.ip);
    }
  });
  
  return todayVisitors.size;
};

export default {
  isTodayInTunisia,
  getTunisiaOffset,
  convertToTunisiaTime,
  getTodayRangeInTunisia,
  getTimezoneDebugInfo,
  calculateTodayVisitorsInTunisia
};
