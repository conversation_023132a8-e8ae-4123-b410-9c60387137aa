import React, { useCallback, useState } from 'react';
import { Link } from 'react-router-dom';
import { getSortedJobsData } from '../data/jobsData';
import { useVisitorTracking } from '../hooks/useVisitorTracking';
import { logDebug } from '../utils/logger';
import { API_CONFIG } from '../config/apiConfig';
import LockedJobNotification from './LockedJobNotification';

const Experience = () => {
  const [showLockedNotification, setShowLockedNotification] = useState(false);
  const [selectedLockedJob, setSelectedLockedJob] = useState(null);

  const { ref } = useVisitorTracking('experience', {
    threshold: 0.4,
    minDuration: 2 // Reduced from 5 to 2 seconds for better tracking
  });

  const handleCompanyLinkClick = (e) => {
    // Prevent the parent Link from being triggered when clicking the company link
    e.stopPropagation();
  };

  const handleTimelineItemClick = (job) => {
    // Check if job is locked
    if (job.isLocked) {
      setSelectedLockedJob(job);
      setShowLockedNotification(true);
      return;
    }

    // Store the current scroll position for this specific job
    const currentScrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    sessionStorage.setItem(`timeline-scroll-${job.slug}`, currentScrollPosition.toString());
  };

  // Experience project tracking function
  const trackExperienceInteraction = useCallback(async (job, interactionType = 'click') => {
    const API_URL = API_CONFIG.BASE_URL;
    if (!API_URL) return;

    try {
      await fetch(API_CONFIG.ENDPOINTS.TRACK_VISIT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section: `experience-item-${interactionType}`,
          duration: 1, // Minimal duration for interaction tracking
          sessionId: `${Date.now()}-${Math.random()}`,
          pageUrl: window.location.href,
          jobTitle: job.title,
          jobSlug: job.slug,
          projectTitle: job.title,
          projectType: 'experience',
          projectId: `experience-${job.id}-${job.slug}`,
          projectAvailable: true, // Experience items are always available
          projectUrl: `/job/${job.slug}`,
          interactionType: interactionType
        }),
      });

      logDebug(`📊 Experience Project Interaction: ${job.title} - ${interactionType}`, {
        jobSlug: job.slug,
        jobId: job.id
      });
    } catch (error) {
      console.warn('Experience project interaction tracking failed:', error);
    }
  }, []);

  // Get sorted jobs data
  const sortedJobs = getSortedJobsData();

  return (
    <section className="experience" ref={ref}>
      <h2>Professional Experience</h2>
      <div className="timeline">
        {sortedJobs.map((job) => (
          <div key={job.id} className="timeline-item">
            <div className="timeline-dot"></div>
            {job.isLocked ? (
              // Render locked job as a clickable div instead of Link
              <div
                className="timeline-content-link locked-job"
                onClick={() => {
                  handleTimelineItemClick(job);
                  trackExperienceInteraction(job, 'click');
                }}
                onMouseEnter={() => trackExperienceInteraction(job, 'hover')}
                style={{ cursor: 'pointer' }}
              >
                <div className="timeline-content">
                  <img
                    src={job.logo}
                    alt={job.logoAlt}
                    className="company-logo"
                  />
                  <h3 className="job-title">{job.title}</h3>
                  <h4 className="company-name">{job.company}</h4>
                  {job.companyLink && job.companyLink.trim() && (
                    <p className="company-link">
                      <span
                        className="company-link-span"
                        onClick={handleCompanyLinkClick}
                        style={{ color: '#4B0082', textDecoration: 'underline', cursor: 'pointer' }}
                        role="link"
                        tabIndex={0}
                        onKeyDown={e => { if (e.key === 'Enter') window.open(job.companyLink, '_blank'); }}
                      >
                        {job.companyLink}
                      </span>
                    </p>
                  )}
                  <p className="job-duration">{job.duration}</p>
                  <p className="job-description">{job.summary}</p>
                  <div className="locked-indicator">
                    <span className="lock-icon">🔒</span>
                    <span className="locked-text">Available Soon</span>
                  </div>
                </div>
              </div>
            ) : (
              // Render normal job as Link
              <Link
                to={`/job/${job.slug}`}
                className="timeline-content-link"
                onClick={() => {
                  handleTimelineItemClick(job);
                  trackExperienceInteraction(job, 'click');
                }}
                onMouseEnter={() => trackExperienceInteraction(job, 'hover')}
              >
                <div className="timeline-content">
                  <img
                    src={job.logo}
                    alt={job.logoAlt}
                    className="company-logo"
                  />
                  <h3 className="job-title">{job.title}</h3>
                  <h4 className="company-name">{job.company}</h4>
                  {job.companyLink && (
                    <p className="company-link">
                      <span
                        className="company-link-span"
                        onClick={handleCompanyLinkClick}
                        style={{ color: '#4B0082', textDecoration: 'underline', cursor: 'pointer' }}
                        role="link"
                        tabIndex={0}
                        onKeyDown={e => { if (e.key === 'Enter') window.open(job.companyLink, '_blank'); }}
                      >
                        {job.companyLink}
                      </span>
                    </p>
                  )}
                  <p className="job-duration">{job.duration}</p>
                  <p className="job-description">{job.summary}</p>
                  <div className="view-details">
                    <span>View Details →</span>
                  </div>
                </div>
              </Link>
            )}
          </div>
        ))}
      </div>

      {/* Locked Job Notification */}
      <LockedJobNotification
        isOpen={showLockedNotification}
        onClose={() => setShowLockedNotification(false)}
        jobTitle={selectedLockedJob?.title || ''}
      />
    </section>
  );
};

export default Experience;
