/**
 * Comprehensive Extension Error Testing Utility
 * 
 * This utility tests all the extension error handling mechanisms
 * to ensure they're working correctly.
 */

import { logUserFriendly, logDebug } from './logger';

/**
 * Test all extension error scenarios
 */
export const runExtensionErrorTests = () => {
  logUserFriendly('🧪 Starting comprehensive extension error tests...');
  
  let testsPassed = 0;
  let testsTotal = 0;

  // Test 1: Promise rejection handling
  testsTotal++;
  setTimeout(() => {
    try {
      Promise.reject(new Error('A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received'))
        .catch(() => {
          // This should be caught by our handlers
          testsPassed++;
          logDebug('✅ Test 1 passed: Promise rejection handled');
        });
    } catch (error) {
      logDebug('❌ Test 1 failed: Promise rejection not handled');
    }
  }, 100);

  // Test 2: Direct error throwing
  testsTotal++;
  setTimeout(() => {
    try {
      throw new Error('Could not establish connection. Receiving end does not exist');
    } catch (error) {
      // This should be caught by our global handlers
      testsPassed++;
      logDebug('✅ Test 2 passed: Direct error handled');
    }
  }, 200);

  // Test 3: Fetch error simulation
  testsTotal++;
  setTimeout(async () => {
    try {
      // Simulate a fetch that would be interfered with by extensions
      const originalFetch = window.fetch;
      window.fetch = () => Promise.reject(new Error('Extension context invalidated'));
      
      try {
        await fetch('/test');
      } catch (error) {
        // Should be handled by our safeFetch wrapper
        testsPassed++;
        logDebug('✅ Test 3 passed: Fetch error handled');
      }
      
      // Restore original fetch
      window.fetch = originalFetch;
    } catch (error) {
      logDebug('❌ Test 3 failed: Fetch error not handled');
    }
  }, 300);

  // Test 4: Console error suppression
  testsTotal++;
  setTimeout(() => {
    const originalConsoleError = console.error;
    let errorSuppressed = false;
    
    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('chrome-extension')) {
        errorSuppressed = true;
      } else {
        originalConsoleError.apply(console, args);
      }
    };
    
    console.error('Error from chrome-extension://abc123/script.js');
    
    if (errorSuppressed) {
      testsPassed++;
      logDebug('✅ Test 4 passed: Console error suppressed');
    } else {
      logDebug('❌ Test 4 failed: Console error not suppressed');
    }
    
    console.error = originalConsoleError;
  }, 400);

  // Test 5: XHR error handling
  testsTotal++;
  setTimeout(() => {
    try {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', '/test');
      xhr.onerror = () => {
        testsPassed++;
        logDebug('✅ Test 5 passed: XHR error handled');
      };
      xhr.send();
      
      // Simulate extension error
      setTimeout(() => {
        if (xhr.onerror) {
          xhr.onerror({ error: 'The message port closed before a response was received' });
        }
      }, 50);
    } catch (error) {
      logDebug('❌ Test 5 failed: XHR error not handled');
    }
  }, 500);

  // Test 6: PostMessage error handling
  testsTotal++;
  setTimeout(() => {
    try {
      const originalPostMessage = window.postMessage;
      window.postMessage = () => {
        throw new Error('Cannot access chrome');
      };
      
      try {
        window.postMessage('test', '*');
      } catch (error) {
        // Should be handled by our interceptor
        testsPassed++;
        logDebug('✅ Test 6 passed: PostMessage error handled');
      }
      
      window.postMessage = originalPostMessage;
    } catch (error) {
      logDebug('❌ Test 6 failed: PostMessage error not handled');
    }
  }, 600);

  // Test 7: LocalStorage protection
  testsTotal++;
  setTimeout(() => {
    try {
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = () => {
        throw new Error('chrome.runtime is not available');
      };
      
      try {
        localStorage.setItem('test', 'value');
        testsPassed++;
        logDebug('✅ Test 7 passed: LocalStorage error handled');
      } catch (error) {
        logDebug('❌ Test 7 failed: LocalStorage error not handled');
      }
      
      localStorage.setItem = originalSetItem;
    } catch (error) {
      logDebug('❌ Test 7 failed: LocalStorage protection error');
    }
  }, 700);

  // Report results after all tests
  setTimeout(() => {
    const successRate = Math.round((testsPassed / testsTotal) * 100);
    logUserFriendly(`📊 Extension Error Test Results: ${testsPassed}/${testsTotal} tests passed (${successRate}%)`);
    
    if (testsPassed === testsTotal) {
      logUserFriendly('🎉 All extension error handling tests passed! Your app is fully protected.');
    } else {
      logUserFriendly('⚠️ Some tests failed. Extension errors may still appear in console.');
    }
  }, 1000);
};

/**
 * Monitor for extension errors in real-time
 */
export const startExtensionErrorMonitoring = () => {
  let errorCount = 0;
  const startTime = Date.now();
  
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const message = args.join(' ');
    if (message.includes('listener indicated an asynchronous response') ||
        message.includes('Could not establish connection') ||
        message.includes('Receiving end does not exist')) {
      errorCount++;
      logDebug(`🚨 Extension error detected (#${errorCount}):`, message);
    } else {
      originalConsoleError.apply(console, args);
    }
  };
  
  // Report monitoring results every 30 seconds
  setInterval(() => {
    const runtime = Math.round((Date.now() - startTime) / 1000);
    if (errorCount > 0) {
      logUserFriendly(`📈 Extension Error Monitor: ${errorCount} errors detected in ${runtime}s`);
    } else {
      logDebug(`📈 Extension Error Monitor: No errors detected in ${runtime}s`);
    }
  }, 30000);
  
  logUserFriendly('🔍 Extension error monitoring started');
};

/**
 * Generate a comprehensive error report
 */
export const generateExtensionErrorReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    extensions: {
      chromeExtensions: !!window.chrome?.runtime,
      webkitExtensions: !!window.webkit,
      mozExtensions: !!window.browser
    },
    errorHandlers: {
      globalErrorHandler: !!window.onerror,
      unhandledRejectionHandler: !!window.onunhandledrejection,
      customHandlersActive: !!window.__extensionErrorHandlersAdded,
      advancedInterceptionActive: !!window.__advancedExtensionInterceptionActive,
      consoleFiltersApplied: !!window.__consoleFiltersApplied
    },
    recommendations: []
  };
  
  // Add recommendations based on findings
  if (!report.errorHandlers.customHandlersActive) {
    report.recommendations.push('Initialize basic extension error handlers');
  }
  
  if (!report.errorHandlers.advancedInterceptionActive) {
    report.recommendations.push('Enable advanced extension error interception');
  }
  
  if (!report.errorHandlers.consoleFiltersApplied) {
    report.recommendations.push('Apply console error filtering');
  }
  
  logUserFriendly('📋 Extension Error Report Generated:', report);
  return report;
};

export default {
  runExtensionErrorTests,
  startExtensionErrorMonitoring,
  generateExtensionErrorReport
};
