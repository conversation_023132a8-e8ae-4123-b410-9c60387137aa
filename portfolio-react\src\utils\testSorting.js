// Test file to verify the sorting functionality
import { getSortedJobsData } from '../data/jobsData';

// Function to test the sorting
export const testJobsSorting = () => {
  const sortedJobs = getSortedJobsData();
  
  console.log('=== Jobs Sorting Test ===');
  console.log('Jobs sorted by most recent end date:');
  
  sortedJobs.forEach((job, index) => {
    console.log(`${index + 1}. ${job.title} - Duration: ${job.duration} - ID: ${job.id}${job.isLocked ? ' (LOCKED)' : ''}`);
  });
  
  console.log('\nExpected order:');
  console.log('1. Mediscan (6/2025 - Now) - Most recent (current)');
  console.log('2. Frontend Developer Angular (2/2025 - 6/2025) - Second most recent');
  console.log('3. 3D-ecommerce platform (2022 - 2023) - Oldest');
  
  return sortedJobs;
};

// Run the test if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment - can be called from console
  window.testJobsSorting = testJobsSorting;
}
